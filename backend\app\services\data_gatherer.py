"""
Data Gathering Service
=====================

Handles gathering status data from the recoater hardware for WebSocket broadcasting.
Provides efficient, concurrent data collection with error handling.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Set

from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

logger = logging.getLogger(__name__)


class RecoaterDataGatherer:
    """Handles gathering status data from recoater hardware."""

    def __init__(self):
        """Initialize the data gatherer."""
        pass

    async def _safe_api_call(self, method, *args) -> Optional[Any]:
        """
        Safely execute an API call with error handling.
        
        Args:
            method: The API method to call
            *args: Arguments to pass to the method
            
        Returns:
            API response or None if failed
        """
        try:
            return await asyncio.to_thread(method, *args)
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"API call failed: {e}")
            return None

    async def gather_single_drum_data(self, client: Any, drum_id: int) -> Dict[str, Any]:
        """
        Gather all status data for a single drum.
        
        Args:
            client: The recoater client instance
            drum_id: ID of the drum to gather data for
            
        Returns:
            Dictionary containing drum status data
        """
        # Use asyncio.to_thread for synchronous client methods to avoid blocking
        info = await asyncio.to_thread(client.get_drum, drum_id)

        # If basic info fails, we can't proceed for this drum
        if info is None:
            logger.debug(f"Could not retrieve basic info for drum {drum_id}.")
            return {}
            
        return {
            "info": info,
            "motion": await self._safe_api_call(client.get_drum_motion, drum_id),
            "ejection": await self._safe_api_call(client.get_drum_ejection, drum_id),
            "suction": await self._safe_api_call(client.get_drum_suction, drum_id),
            "blade_screws": await self._safe_api_call(client.get_blade_screws_info, drum_id),
            "blade_motion": await self._safe_api_call(client.get_blade_screws_motion, drum_id),
        }

    async def gather_all_drum_data(self, client: Any) -> Optional[Dict[str, Any]]:
        """
        Gather status data for all drums concurrently.
        
        Args:
            client: The recoater client instance
            
        Returns:
            Dictionary containing all drums' status data or None if failed
        """
        try:
            drums_info = await asyncio.to_thread(client.get_drums)
            if not isinstance(drums_info, list):
                logger.debug("Drums info is not a list. Cannot gather drum data.")
                return None
            
            all_drum_data = {}
            for drum_info in drums_info:
                drum_id = drum_info.get("id")
                if drum_id is not None:
                    drum_data = await self.gather_single_drum_data(client, drum_id)
                    if drum_data:
                        all_drum_data[str(drum_id)] = drum_data
            return all_drum_data
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"Drums data not available: {e}")
            return None
        
    async def gather_leveler_data(self, client: Any) -> Optional[Dict[str, Any]]:
        """
        Gather leveler status data.
        
        Args:
            client: The recoater client instance
            
        Returns:
            Dictionary containing leveler status data or None if failed
        """
        try:
            return {
                "pressure": await asyncio.to_thread(client.get_leveler_pressure),
                "sensor": await asyncio.to_thread(client.get_leveler_sensor)
            }
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"Leveler data not available: {e}")
            return None
        
    async def gather_print_data(self, client: Any) -> Optional[Dict[str, Any]]:
        """
        Gather print status data.
        
        Args:
            client: The recoater client instance
            
        Returns:
            Dictionary containing print status data or None if failed
        """
        try:
            return {
                "layer_parameters": await asyncio.to_thread(client.get_layer_parameters),
                "job_status": await asyncio.to_thread(client.get_print_job_status)
            }
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"Print data not available: {e}")
            return None

    async def gather_axis_data(self, client: Any) -> Optional[Dict[str, Any]]:
        """
        Gather axis status data.
        
        Args:
            client: The recoater client instance
            
        Returns:
            Dictionary containing axis status data or None if failed
        """
        try:
            # Note: Axis endpoints are not available in current hardware API
            # This is a placeholder for when axis control becomes available
            return {
                "x": {"position": 0.0, "running": False},
                "z": {"position": 0.0, "running": False},
                "gripper": {"enabled": False}
            }
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            logger.debug(f"Axis data not available: {e}")
            return None

    async def gather_all_data(self, client: Any, required_data_types: Set[str]) -> Dict[str, Any]:
        """
        Gather all required data types concurrently.
        
        Args:
            client: The recoater client instance
            required_data_types: Set of data types to gather
            
        Returns:
            Dictionary containing all gathered data
        """
        # Always get basic status
        logger.debug("Calling: get_state()")
        main_status = await asyncio.to_thread(client.get_state)

        # Conditionally gather other data types based on subscriptions
        gather_tasks = []

        if "axis" in required_data_types:
            logger.debug("Gathering AXIS data: get_axis_*() calls")
            gather_tasks.append(self.gather_axis_data(client))
        else:
            gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

        if "drum" in required_data_types:
            logger.debug("Gathering DRUM data: get_drums(), get_drum(), get_drum_motion(), get_drum_ejection(), get_drum_suction(), get_blade_screws_info(), get_blade_screws_motion() for each drum")
            gather_tasks.append(self.gather_all_drum_data(client))
        else:
            gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

        if "leveler" in required_data_types:
            logger.debug("Gathering LEVELER data: get_leveler_pressure(), get_leveler_sensor()")
            gather_tasks.append(self.gather_leveler_data(client))
        else:
            gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

        if "print" in required_data_types:
            logger.debug("Gathering PRINT data: get_layer_parameters(), get_print_job_status()")
            gather_tasks.append(self.gather_print_data(client))
        else:
            gather_tasks.append(asyncio.create_task(asyncio.sleep(0, result=None)))

        # Execute only the required data gathering tasks
        axis_data, drum_data, leveler_data, print_data = await asyncio.gather(*gather_tasks)

        return {
            "status": main_status,
            "axis_data": axis_data,
            "drum_data": drum_data,
            "leveler_data": leveler_data,
            "print_data": print_data
        }

    def construct_status_message(self, gathered_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Construct a status message to be sent over WebSocket.
        
        Args:
            gathered_data: Dictionary containing all gathered data
            
        Returns:
            Formatted status message
        """
        return {
            "type": "status_update",
            "data": gathered_data.get("status"),
            "axis_data": gathered_data.get("axis_data"),
            "drum_data": gathered_data.get("drum_data"),
            "leveler_data": gathered_data.get("leveler_data"),
            "print_data": gathered_data.get("print_data"),
            "timestamp": asyncio.get_event_loop().time()
        }
