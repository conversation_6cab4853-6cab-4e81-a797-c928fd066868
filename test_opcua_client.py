#!/usr/bin/env python3
"""
OPC UA Client Test Script
Tests the OPC UA server functionality by connecting and reading all nodes.
"""

import asyncio
import logging
from asyncua import Client
from asyncua.common.node import No<PERSON>
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OPCUAClientTester:
    """Test client for OPC UA server functionality."""
    
    def __init__(self, endpoint: str = "opc.tcp://localhost:4843/recoater/server/"):
        self.endpoint = endpoint
        self.client = None
        
    async def connect(self) -> bool:
        """Connect to the OPC UA server."""
        try:
            self.client = Client(self.endpoint)
            await self.client.connect()
            logger.info(f"Connected to OPC UA server at {self.endpoint}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to OPC UA server: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the OPC UA server."""
        if self.client:
            await self.client.disconnect()
            logger.info("Disconnected from OPC UA server")
    
    async def get_server_info(self) -> Dict[str, Any]:
        """Get basic server information."""
        try:
            # Get server node
            server_node = self.client.get_server_node()
            server_info = {
                "server_array": await server_node.get_server_array(),
                "namespace_array": await server_node.get_namespace_array(),
            }
            
            # Get root node
            root = self.client.get_root_node()
            server_info["root_node"] = str(root)
            
            return server_info
        except Exception as e:
            logger.error(f"Failed to get server info: {e}")
            return {}
    
    async def browse_node(self, node: Node, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
        """Recursively browse a node and its children."""
        if current_depth >= max_depth:
            return {"name": await node.read_display_name(), "node_id": str(node.nodeid), "children": "..."}
        
        try:
            node_info = {
                "name": (await node.read_display_name()).Text,
                "node_id": str(node.nodeid),
                "node_class": str(await node.read_node_class()),
                "children": {}
            }
            
            # Try to read value if it's a variable
            try:
                value = await node.read_value()
                node_info["value"] = value
                node_info["data_type"] = str(await node.read_data_type())
            except:
                pass  # Not a variable or no read access
            
            # Browse children
            children = await node.get_children()
            for child in children:
                child_name = (await child.read_display_name()).Text
                node_info["children"][child_name] = await self.browse_node(child, max_depth, current_depth + 1)
            
            return node_info
        except Exception as e:
            logger.error(f"Error browsing node {node}: {e}")
            return {"error": str(e)}
    
    async def find_coordination_variables(self) -> Dict[str, Any]:
        """Find and read all coordination variables."""
        try:
            # Get objects node
            objects = self.client.get_objects_node()
            
            # Look for RecoaterCoordination folder
            children = await objects.get_children()
            coordination_folder = None
            
            for child in children:
                name = (await child.read_display_name()).Text
                if "RecoaterCoordination" in name or "Coordination" in name:
                    coordination_folder = child
                    break
            
            if not coordination_folder:
                # Try to find variables directly in objects
                logger.info("RecoaterCoordination folder not found, looking for variables in objects node")
                coordination_vars = {}
                for child in children:
                    name = (await child.read_display_name()).Text
                    if any(var_name in name for var_name in [
                        "job_active", "total_layers", "current_layer", 
                        "recoater_ready_to_print", "recoater_layer_complete", 
                        "backend_error", "plc_error"
                    ]):
                        try:
                            value = await child.read_value()
                            coordination_vars[name] = {
                                "value": value,
                                "node_id": str(child.nodeid),
                                "data_type": str(await child.read_data_type())
                            }
                        except Exception as e:
                            coordination_vars[name] = {"error": str(e)}
                return coordination_vars
            
            # Browse coordination folder
            logger.info(f"Found coordination folder: {(await coordination_folder.read_display_name()).Text}")
            coordination_vars = {}
            coord_children = await coordination_folder.get_children()
            
            for var_node in coord_children:
                var_name = (await var_node.read_display_name()).Text
                try:
                    value = await var_node.read_value()
                    coordination_vars[var_name] = {
                        "value": value,
                        "node_id": str(var_node.nodeid),
                        "data_type": str(await var_node.read_data_type())
                    }
                except Exception as e:
                    coordination_vars[var_name] = {"error": str(e)}
            
            return coordination_vars
            
        except Exception as e:
            logger.error(f"Failed to find coordination variables: {e}")
            return {}
    
    async def test_write_operations(self) -> Dict[str, Any]:
        """Test writing to writable variables."""
        results = {}
        
        try:
            # Get objects node and find variables
            objects = self.client.get_objects_node()
            children = await objects.get_children()
            
            # Test variables that should be writable
            writable_vars = ["plc_error", "backend_error"]
            
            for child in children:
                name = (await child.read_display_name()).Text
                if any(var_name in name for var_name in writable_vars):
                    try:
                        # Read current value
                        current_value = await child.read_value()
                        
                        # Try to write opposite boolean value
                        new_value = not current_value if isinstance(current_value, bool) else True
                        await child.write_value(new_value)
                        
                        # Read back to verify
                        written_value = await child.read_value()
                        
                        results[name] = {
                            "original": current_value,
                            "written": new_value,
                            "read_back": written_value,
                            "success": written_value == new_value
                        }
                        
                        # Restore original value
                        await child.write_value(current_value)
                        
                    except Exception as e:
                        results[name] = {"error": str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to test write operations: {e}")
            return {"error": str(e)}

async def main():
    """Main test function."""
    tester = OPCUAClientTester()
    
    print("=" * 60)
    print("OPC UA Server Test")
    print("=" * 60)
    
    # Connect to server
    if not await tester.connect():
        print("❌ Failed to connect to OPC UA server")
        return
    
    try:
        # Get server info
        print("\n📋 Server Information:")
        server_info = await tester.get_server_info()
        for key, value in server_info.items():
            print(f"  {key}: {value}")
        
        # Find coordination variables
        print("\n🎯 Coordination Variables:")
        coord_vars = await tester.find_coordination_variables()
        if coord_vars:
            for var_name, var_info in coord_vars.items():
                if "error" in var_info:
                    print(f"  ❌ {var_name}: {var_info['error']}")
                else:
                    print(f"  ✅ {var_name}: {var_info['value']} (Type: {var_info.get('data_type', 'Unknown')})")
        else:
            print("  ❌ No coordination variables found")
        
        # Test write operations
        print("\n✏️  Write Operation Tests:")
        write_results = await tester.test_write_operations()
        if write_results:
            for var_name, result in write_results.items():
                if "error" in result:
                    print(f"  ❌ {var_name}: {result['error']}")
                else:
                    status = "✅" if result.get("success") else "❌"
                    print(f"  {status} {var_name}: {result['original']} → {result['written']} → {result['read_back']}")
        else:
            print("  ❌ No writable variables found or test failed")
        
        # Browse objects node structure
        print("\n🌳 Objects Node Structure:")
        objects = tester.client.get_objects_node()
        objects_structure = await tester.browse_node(objects, max_depth=2)
        print(f"  Objects Node: {objects_structure.get('name', 'Unknown')}")
        for child_name, child_info in objects_structure.get("children", {}).items():
            print(f"    └── {child_name} ({child_info.get('node_class', 'Unknown')})")
            if "value" in child_info:
                print(f"        Value: {child_info['value']}")
    
    finally:
        await tester.disconnect()
    
    print("\n" + "=" * 60)
    print("OPC UA Test Complete")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
