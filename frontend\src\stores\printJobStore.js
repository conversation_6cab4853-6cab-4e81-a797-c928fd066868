/**
 * Print Job Store
 * ===============
 * 
 * Pinia store for managing multi-material print job state, including
 * 3-drum coordination, job progress, error handling, and real-time updates.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '../services/api'

export const usePrintJobStore = defineStore('printJob', () => {
  // State
  const multiMaterialJob = ref({
    isActive: false,
    jobId: null,
    currentLayer: 0,
    totalLayers: 0,
    progressPercentage: 0,
    status: 'idle', // idle, uploading, printing, waiting, completed, error
    errorMessage: '',
    startTime: null,
    estimatedTimeRemaining: null,
    drums: {
      0: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' },
      1: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' },
      2: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' }
    }
  })

  // Error flags for OPC UA coordination
  const errorFlags = ref({
    backendError: false,
    plcError: false,
    errorMessage: '',
    showCriticalModal: false
  })

  // Loading states
  const isLoading = ref(false)
  const isStartingJob = ref(false)
  const isCancellingJob = ref(false)
  const isClearingErrors = ref(false)

  // File upload state
  const uploadedFiles = ref({
    0: null, // { fileId: string, fileName: string, layerCount: number }
    1: null,
    2: null
  })

  // Last uploaded file tracking for File Management tab
  const lastUploadedFiles = ref({
    0: null, // { fileName: string, uploadTime: Date, source: 'direct' | 'cli_layer' }
    1: null,
    2: null
  })

  // Computed properties
  const isJobActive = computed(() => multiMaterialJob.value.isActive)
  const hasActiveJob = computed(() => multiMaterialJob.value.jobId !== null)
  const hasErrors = computed(() => errorFlags.value.backendError || errorFlags.value.plcError)
  const hasCriticalError = computed(() => errorFlags.value.showCriticalModal)
  
  const allDrumsReady = computed(() => {
    return Object.values(multiMaterialJob.value.drums).every(drum => drum.ready)
  })

  const allFilesUploaded = computed(() => {
    return Object.values(uploadedFiles.value).every(file => file !== null)
  })

  const hasMinimumFiles = computed(() => {
    // For dual/triple material printing, require at least 2 files
    const uploadedCount = Object.values(uploadedFiles.value).filter(file => file !== null).length
    return uploadedCount >= 2
  })

  const canStartJob = computed(() => {
    return hasMinimumFiles.value &&
           !isJobActive.value &&
           !hasErrors.value &&
           !isLoading.value
  })

  const jobProgress = computed(() => {
    if (multiMaterialJob.value.totalLayers === 0) return 0
    return Math.round((multiMaterialJob.value.currentLayer / multiMaterialJob.value.totalLayers) * 100)
  })

  // Actions
  function updateJobStatus(statusData) {
    if (statusData) {
      multiMaterialJob.value = {
        ...multiMaterialJob.value,
        ...statusData,
        progressPercentage: statusData.progress_percentage || 0
      }
    }
  }

  function updateDrumStatus(drumId, drumData) {
    if (multiMaterialJob.value.drums[drumId]) {
      multiMaterialJob.value.drums[drumId] = {
        ...multiMaterialJob.value.drums[drumId],
        ...drumData
      }
    }
  }

  function setErrorFlags(backendError, plcError, message = '') {
    errorFlags.value.backendError = backendError
    errorFlags.value.plcError = plcError
    errorFlags.value.errorMessage = message
    
    // Show critical modal if either error flag is set
    if (backendError || plcError) {
      errorFlags.value.showCriticalModal = true
    }
  }

  function clearErrorFlags() {
    errorFlags.value.backendError = false
    errorFlags.value.plcError = false
    errorFlags.value.errorMessage = ''
    errorFlags.value.showCriticalModal = false
  }

  function closeCriticalModal() {
    errorFlags.value.showCriticalModal = false
  }

  function setFileUploaded(drumId, fileData) {
    uploadedFiles.value[drumId] = fileData
  }

  function clearUploadedFiles() {
    uploadedFiles.value = { 0: null, 1: null, 2: null }
  }

  // File Management tab tracking functions
  function setLastUploadedFile(drumId, fileName, source = 'direct') {
    lastUploadedFiles.value[drumId] = {
      fileName,
      uploadTime: new Date(),
      source
    }
  }

  function clearLastUploadedFiles() {
    lastUploadedFiles.value = { 0: null, 1: null, 2: null }
  }

  function getLastUploadedFileName(drumId) {
    const fileInfo = lastUploadedFiles.value[drumId]
    if (!fileInfo) return 'No file uploaded'

    if (fileInfo.source === 'cli_layer') {
      return fileInfo.fileName // Already includes layer range prefix
    }
    return fileInfo.fileName
  }

  function resetJobState() {
    multiMaterialJob.value = {
      isActive: false,
      jobId: null,
      currentLayer: 0,
      totalLayers: 0,
      progressPercentage: 0,
      status: 'idle',
      errorMessage: '',
      startTime: null,
      estimatedTimeRemaining: null,
      drums: {
        0: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' },
        1: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' },
        2: { fileId: null, ready: false, uploaded: false, status: 'idle', errorMessage: '' }
      }
    }
  }

  // API Actions
  async function startMultiMaterialJob() {
    if (!canStartJob.value) {
      throw new Error('Cannot start job: requirements not met')
    }

    isStartingJob.value = true
    try {
      // Only include drums that have uploaded files
      const fileIds = {}
      for (let drumId = 0; drumId <= 2; drumId++) {
        if (uploadedFiles.value[drumId]) {
          fileIds[drumId] = uploadedFiles.value[drumId].fileId
        }
      }

      // Validate we have at least 2 files for dual/triple material printing
      if (Object.keys(fileIds).length < 2) {
        throw new Error('At least 2 files must be uploaded for dual/triple material printing')
      }

      const response = await apiService.startMultiMaterialJob(fileIds)
      
      if (response.data.success) {
        multiMaterialJob.value.jobId = response.data.job_id
        multiMaterialJob.value.isActive = true
        multiMaterialJob.value.status = 'uploading'
        multiMaterialJob.value.startTime = Date.now()
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to start job')
      }
    } finally {
      isStartingJob.value = false
    }
  }

  async function cancelMultiMaterialJob() {
    if (!hasActiveJob.value) {
      throw new Error('No active job to cancel')
    }

    isCancellingJob.value = true
    try {
      const response = await apiService.cancelMultiMaterialJob()
      
      if (response.data.success) {
        resetJobState()
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to cancel job')
      }
    } finally {
      isCancellingJob.value = false
    }
  }

  async function clearErrorFlagsAPI() {
    isClearingErrors.value = true
    try {
      const response = await apiService.clearErrorFlags()
      
      if (response.data.success) {
        clearErrorFlags()
        return response.data
      } else {
        throw new Error(response.data.message || 'Failed to clear error flags')
      }
    } finally {
      isClearingErrors.value = false
    }
  }

  async function fetchJobStatus() {
    try {
      const response = await apiService.getMultiMaterialJobStatus()
      updateJobStatus(response.data)
      return response.data
    } catch (error) {
      console.error('Failed to fetch job status:', error)
      throw error
    }
  }

  async function fetchDrumStatus(drumId) {
    try {
      const response = await apiService.getDrumStatus(drumId)
      updateDrumStatus(drumId, response.data)
      return response.data
    } catch (error) {
      console.error(`Failed to fetch drum ${drumId} status:`, error)
      throw error
    }
  }

  return {
    // State
    multiMaterialJob,
    errorFlags,
    isLoading,
    isStartingJob,
    isCancellingJob,
    isClearingErrors,
    uploadedFiles,
    lastUploadedFiles,

    // Computed
    isJobActive,
    hasActiveJob,
    hasErrors,
    hasCriticalError,
    allDrumsReady,
    allFilesUploaded,
    hasMinimumFiles,
    canStartJob,
    jobProgress,

    // Actions
    updateJobStatus,
    updateDrumStatus,
    setErrorFlags,
    clearErrorFlags,
    closeCriticalModal,
    setFileUploaded,
    clearUploadedFiles,
    setLastUploadedFile,
    clearLastUploadedFiles,
    getLastUploadedFileName,
    resetJobState,
    startMultiMaterialJob,
    cancelMultiMaterialJob,
    clearErrorFlagsAPI,
    fetchJobStatus,
    fetchDrumStatus
  }
})
