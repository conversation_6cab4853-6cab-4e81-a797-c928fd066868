"""
Recoater HMI Backend - Main Application
=======================================

This is the main FastAPI application that serves as the backend for the
Recoater Custom HMI. It provides REST API endpoints and WebSocket connections
for real-time communication with the frontend.

The application acts as a proxy between the frontend and the recoater hardware,
providing a simplified and stable interface while handling all the complexity
of hardware communication.
"""

import os
import json
import logging
from contextlib import asynccontextmanager # Manages setup and teardown in asynchronous code

from fastapi import FastAPI, WebSocket, WebSocketDisconnect # Tools for handling HTTP requests and WebSocket connections
from fastapi.middleware.cors import CORSMiddleware # Tool to handle a web security feature called Cross-Origin Resource Sharing (CORS)
from dotenv import load_dotenv # Helper library to load configuration settings from a separate file (usually .env)

from app.dependencies import initialize_recoater_client, initialize_opcua_coordinator, initialize_multilayer_job_manager
from app.utils.heartbeat import start_heartbeat_task, stop_heartbeat_task
from app.services.websocket_manager import WebSocketConnectionManager
from app.services.status_poller import StatusPollingService
# app.api.* are "routers" that group related API endpoints together
from app.api.status import router as status_router
from app.api.axis import router as axis_router
from app.api.recoater_controls import router as recoater_router
from app.api.print import router as print_router
from app.api.configuration import router as config_router

# Looks for .env in the project folder and loads any variables from it into the environment
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create global service instances
websocket_manager = WebSocketConnectionManager()
status_poller = StatusPollingService(websocket_manager)



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""

    # Startup
    logger.info("Starting Recoater HMI Backend...")

    # Initialize recoater client using the dependencies module
    initialize_recoater_client()

    # Initialize multilayer job manager for multi-material coordination
    initialize_multilayer_job_manager()

    # Initialize OPC UA coordinator for multi-material coordination
    await initialize_opcua_coordinator()

    # Start background status polling task
    await status_poller.start()

    # Start heartbeat task to keep hardware connection alive
    heartbeat_task = start_heartbeat_task()
    logger.info("Heartbeat task started")

    logger.info("Backend startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Recoater HMI Backend...")

    # Stop status polling task
    await status_poller.stop()

    # Stop heartbeat task
    try:
        await stop_heartbeat_task()
    except Exception as e:
        logger.error(f"Error stopping heartbeat task: {e}")
    else:
        logger.info("Heartbeat task stopped")

    logger.info("Backend shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Recoater HMI Backend",
    description="Backend API for the Aerosint SPD Recoater Custom HMI",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(status_router, prefix="/api/v1")
app.include_router(axis_router, prefix="/api/v1")
app.include_router(recoater_router, prefix="/api/v1")
app.include_router(print_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Handle incoming messages for subscription updates
            message_text = await websocket.receive_text()
            try:
                message = json.loads(message_text)
                if message.get("type") == "subscribe" and "data_types" in message:
                    websocket_manager.update_subscription(websocket, message["data_types"])
                    logger.info(f"Updated subscription: {message['data_types']}")
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {message_text}")
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

@app.get("/")
async def root():
    """Root endpoint for basic health check."""
    return {
        "message": "Recoater HMI Backend",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )