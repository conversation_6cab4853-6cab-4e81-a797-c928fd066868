"""
Unit Tests for OPC UA Infrastructure
====================================

Tests for OPC UA server manager, coordinator, and configuration components.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.config.opcua_config import (
    OPCUAServerConfig, 
    CoordinationVariable, 
    get_opcua_config,
    get_variable_by_name,
    COORDINATION_VARIABLES
)
from app.services.opcua_server import OPCUAServerManager
from app.services.opcua_coordinator import OPCUACoordinator


class TestOPCUAConfig:
    """Test OPC UA configuration functionality."""
    
    def test_opcua_server_config_defaults(self):
        """Test default configuration values."""
        config = OPCUAServerConfig()

        assert config.endpoint == "opc.tcp://0.0.0.0:4844/recoater/server/"
        assert config.server_name == "Recoater Multi-Material Coordination Server"
        assert config.namespace_uri == "http://recoater.backend.server"
        assert config.namespace_idx == 2
        assert config.security_policy == "None"
        assert config.auto_restart is True
    
    def test_coordination_variable_creation(self):
        """Test coordination variable definition."""
        var = CoordinationVariable(
            name="test_var",
            node_id="ns=2;s=test_var",
            data_type="Boolean",
            initial_value=False,
            description="Test variable"
        )
        
        assert var.name == "test_var"
        assert var.node_id == "ns=2;s=test_var"
        assert var.data_type == "Boolean"
        assert var.initial_value is False
        assert var.writable is True
        assert var.description == "Test variable"
    
    def test_get_variable_by_name(self):
        """Test variable lookup by name."""
        var = get_variable_by_name("job_active")
        
        assert var.name == "job_active"
        assert var.data_type == "Boolean"
        assert var.initial_value is False
    
    def test_get_variable_by_name_not_found(self):
        """Test variable lookup with invalid name."""
        with pytest.raises(ValueError, match="Coordination variable 'invalid' not found"):
            get_variable_by_name("invalid")
    
    def test_coordination_variables_completeness(self):
        """Test that all required coordination variables are defined."""
        required_vars = [
            "job_active", "total_layers", "current_layer",
            "recoater_ready_to_print", "recoater_layer_complete",
            "backend_error", "plc_error"
        ]

        defined_vars = [var.name for var in COORDINATION_VARIABLES]

        for required_var in required_vars:
            assert required_var in defined_vars, f"Required variable {required_var} not defined"
    
    @patch.dict('os.environ', {
        'OPCUA_SERVER_ENDPOINT': 'opc.tcp://test:4840/test/',
        'OPCUA_NAMESPACE_URI': 'http://test.namespace',
        'OPCUA_NAMESPACE_IDX': '3'
    })
    def test_get_opcua_config_from_env(self):
        """Test configuration loading from environment variables."""
        config = get_opcua_config()
        
        assert config.endpoint == 'opc.tcp://test:4840/test/'
        assert config.namespace_uri == 'http://test.namespace'
        assert config.namespace_idx == 3


class TestOPCUAServerManager:
    """Test OPC UA server manager functionality."""
    
    @pytest.fixture
    def server_manager(self):
        """Create server manager instance for testing."""
        config = OPCUAServerConfig(
            endpoint="opc.tcp://localhost:4840/test/",
            auto_restart=False
        )
        return OPCUAServerManager(config)
    
    def test_server_manager_initialization(self, server_manager):
        """Test server manager initialization."""
        assert server_manager.config.endpoint == "opc.tcp://localhost:4840/test/"
        assert server_manager.server is None
        assert server_manager.namespace_idx == 0
        assert server_manager.variable_nodes == {}
        assert not server_manager.is_running
    
    @patch('app.services.opcua_server.Server')
    @pytest.mark.asyncio
    async def test_server_startup_failure(self, mock_server_class, server_manager):
        """Test server startup failure handling."""
        mock_server = AsyncMock()
        mock_server.init.side_effect = Exception("Connection failed")
        mock_server_class.return_value = mock_server

        result = await server_manager.start_server()

        assert result is False
        assert not server_manager.is_running
    
    def test_get_ua_data_type_mapping(self, server_manager):
        """Test OPC UA data type mapping."""
        from asyncua import ua
        
        assert server_manager._get_ua_data_type("Boolean") == ua.VariantType.Boolean
        assert server_manager._get_ua_data_type("String") == ua.VariantType.String
        assert server_manager._get_ua_data_type("Int32") == ua.VariantType.Int32
        assert server_manager._get_ua_data_type("Float") == ua.VariantType.Float
        assert server_manager._get_ua_data_type("DateTime") == ua.VariantType.DateTime
        assert server_manager._get_ua_data_type("Unknown") == ua.VariantType.String
    
    @pytest.mark.asyncio
    async def test_write_variable_server_not_running(self, server_manager):
        """Test writing variable when server is not running."""
        result = await server_manager.write_variable("test_var", True)

        assert result is False

    @pytest.mark.asyncio
    async def test_read_variable_server_not_running(self, server_manager):
        """Test reading variable when server is not running."""
        result = await server_manager.read_variable("test_var")

        assert result is None
    
    def test_get_variable_names(self, server_manager):
        """Test getting variable names."""
        # Initially empty
        names = server_manager.get_variable_names()
        assert names == []
        
        # Add mock variable
        server_manager.variable_nodes["test_var"] = Mock()
        names = server_manager.get_variable_names()
        assert names == ["test_var"]


class TestOPCUACoordinator:
    """Test OPC UA coordinator functionality."""
    
    @pytest.fixture
    def coordinator(self):
        """Create coordinator instance for testing."""
        config = OPCUAServerConfig(auto_restart=False)
        return OPCUACoordinator(config)
    
    def test_coordinator_initialization(self, coordinator):
        """Test coordinator initialization."""
        assert coordinator.config is not None
        assert not coordinator.is_connected()
        assert coordinator._monitoring_task is None
        assert coordinator._event_handlers == {}
    
    @pytest.mark.asyncio
    async def test_connect_already_connected(self, coordinator):
        """Test connecting when already connected."""
        coordinator._connected = True

        result = await coordinator.connect()

        assert result is True

    @pytest.mark.asyncio
    async def test_disconnect_not_connected(self, coordinator):
        """Test disconnecting when not connected."""
        result = await coordinator.disconnect()

        assert result is True

    @pytest.mark.asyncio
    async def test_write_variable_not_connected(self, coordinator):
        """Test writing variable when not connected."""
        result = await coordinator.write_variable("test_var", True)

        assert result is False

    @pytest.mark.asyncio
    async def test_read_variable_not_connected(self, coordinator):
        """Test reading variable when not connected."""
        result = await coordinator.read_variable("test_var")

        assert result is None
    
    @pytest.mark.asyncio
    async def test_subscribe_to_changes(self, coordinator):
        """Test subscribing to variable changes."""
        handler = Mock()
        variables = ["job_active", "current_layer"]

        result = await coordinator.subscribe_to_changes(variables, handler)

        assert result is True
        assert "job_active" in coordinator._event_handlers
        assert "current_layer" in coordinator._event_handlers
        assert handler in coordinator._event_handlers["job_active"]
        assert handler in coordinator._event_handlers["current_layer"]

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_job_active(self, mock_write, coordinator):
        """Test setting job as active."""
        mock_write.return_value = True

        result = await coordinator.set_job_active(10)

        assert result is True
        assert mock_write.call_count == 3  # 3 variables should be written

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_job_inactive(self, mock_write, coordinator):
        """Test setting job as inactive."""
        mock_write.return_value = True

        result = await coordinator.set_job_inactive()

        assert result is True
        assert mock_write.call_count == 5  # 5 variables should be written
    
    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_update_layer_progress(self, mock_write, coordinator):
        """Test updating layer progress."""
        mock_write.return_value = True

        result = await coordinator.update_layer_progress(5)

        assert result is True
        mock_write.assert_called_once_with("current_layer", 5)

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_recoater_ready_to_print(self, mock_write, coordinator):
        """Test setting recoater ready to print status."""
        mock_write.return_value = True

        result = await coordinator.set_recoater_ready_to_print(True)

        assert result is True
        mock_write.assert_called_once_with("recoater_ready_to_print", True)

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_recoater_layer_complete(self, mock_write, coordinator):
        """Test setting recoater layer complete status."""
        mock_write.return_value = True

        result = await coordinator.set_recoater_layer_complete(True)

        assert result is True
        mock_write.assert_called_once_with("recoater_layer_complete", True)

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_backend_error(self, mock_write, coordinator):
        """Test setting backend error state."""
        mock_write.return_value = True

        result = await coordinator.set_backend_error(True)

        assert result is True
        mock_write.assert_called_once_with("backend_error", True)

    @patch.object(OPCUACoordinator, 'write_variable')
    @pytest.mark.asyncio
    async def test_set_plc_error(self, mock_write, coordinator):
        """Test setting PLC error state."""
        mock_write.return_value = True

        result = await coordinator.set_plc_error(True)

        assert result is True
        mock_write.assert_called_once_with("plc_error", True)

    @patch.object(OPCUACoordinator, 'set_backend_error')
    @patch.object(OPCUACoordinator, 'set_plc_error')
    @pytest.mark.asyncio
    async def test_clear_error_flags(self, mock_set_plc, mock_set_backend, coordinator):
        """Test clearing error flags."""
        mock_set_backend.return_value = True
        mock_set_plc.return_value = True

        result = await coordinator.clear_error_flags()

        assert result is True
        mock_set_backend.assert_called_once_with(False)
        mock_set_plc.assert_called_once_with(False)
    
    def test_get_server_status(self, coordinator):
        """Test getting server status."""
        status = coordinator.get_server_status()
        
        assert "connected" in status
        assert "server_running" in status
        assert "endpoint" in status
        assert "namespace" in status
        assert "variable_count" in status
        assert status["connected"] is False


@pytest.mark.asyncio
class TestOPCUAIntegration:
    """Integration tests for OPC UA components."""
    
    @pytest.mark.asyncio
    async def test_coordinator_server_integration(self):
        """Test coordinator and server manager integration."""
        # This would be a more complex integration test
        # For now, just test that components can be created together
        config = OPCUAServerConfig(auto_restart=False)
        coordinator = OPCUACoordinator(config)

        assert coordinator.server_manager is not None
        assert not coordinator.is_connected()

        # Test status retrieval
        status = coordinator.get_server_status()
        assert status["connected"] is False
