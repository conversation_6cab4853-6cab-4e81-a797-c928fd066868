<template>
  <div class="job-progress-display">
    <div class="progress-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Job Progress</h3>
        <div class="job-info">
          <span v-if="printJobStore.multiMaterialJob.jobId" class="job-id">
            Job ID: {{ printJobStore.multiMaterialJob.jobId }}
          </span>
        </div>
      </div>

      <div class="card-content">
        <!-- Overall Progress -->
        <div class="overall-progress-section">
          <div class="progress-header">
            <h4 class="section-title">Overall Progress</h4>
            <div class="progress-stats">
              <span class="current-layer">
                Layer {{ printJobStore.multiMaterialJob.currentLayer }} of {{ printJobStore.multiMaterialJob.totalLayers }}
              </span>
              <span class="progress-percentage">
                {{ printJobStore.jobProgress }}%
              </span>
            </div>
          </div>
          
          <div class="progress-bar-container">
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: `${printJobStore.jobProgress}%` }"
              ></div>
            </div>
          </div>

          <div class="time-info" v-if="printJobStore.multiMaterialJob.startTime">
            <div class="time-item">
              <span class="time-label">Started:</span>
              <span class="time-value">{{ formatTime(printJobStore.multiMaterialJob.startTime) }}</span>
            </div>
            <div class="time-item">
              <span class="time-label">Elapsed:</span>
              <span class="time-value">{{ getElapsedTime() }}</span>
            </div>
            <div class="time-item" v-if="printJobStore.multiMaterialJob.estimatedTimeRemaining">
              <span class="time-label">Remaining:</span>
              <span class="time-value">{{ formatDuration(printJobStore.multiMaterialJob.estimatedTimeRemaining) }}</span>
            </div>
          </div>
        </div>

        <!-- Drum Status Grid -->
        <div class="drum-status-section">
          <h4 class="section-title">Drum Status</h4>
          <div class="drum-grid">
            <div 
              v-for="drumId in [0, 1, 2]" 
              :key="drumId"
              class="drum-status-card"
              :class="getDrumCardClass(drumId)"
            >
              <div class="drum-header">
                <div class="drum-info">
                  <span class="drum-title">Drum {{ drumId }}</span>
                  <div class="drum-status-indicator">
                    <div 
                      :class="[
                        'status-dot',
                        getDrumStatusClass(drumId)
                      ]"
                    ></div>
                    <span class="status-text">{{ getDrumStatusText(drumId) }}</span>
                  </div>
                </div>
              </div>

              <div class="drum-details">
                <div class="detail-item" v-if="printJobStore.uploadedFiles[drumId]">
                  <span class="detail-label">File:</span>
                  <span class="detail-value">{{ printJobStore.uploadedFiles[drumId].fileName }}</span>
                </div>
                <div class="detail-item" v-if="printJobStore.uploadedFiles[drumId]">
                  <span class="detail-label">Layers:</span>
                  <span class="detail-value">{{ printJobStore.uploadedFiles[drumId].layerCount }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Ready:</span>
                  <span class="detail-value">
                    <span :class="['status-icon', printJobStore.multiMaterialJob.drums[drumId].ready ? 'ready' : 'not-ready']">
                      {{ printJobStore.multiMaterialJob.drums[drumId].ready ? '✓' : '○' }}
                    </span>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Uploaded:</span>
                  <span class="detail-value">
                    <span :class="['status-icon', printJobStore.multiMaterialJob.drums[drumId].uploaded ? 'ready' : 'not-ready']">
                      {{ printJobStore.multiMaterialJob.drums[drumId].uploaded ? '✓' : '○' }}
                    </span>
                  </span>
                </div>
                <div class="detail-item" v-if="printJobStore.multiMaterialJob.drums[drumId].errorMessage">
                  <span class="detail-label">Error:</span>
                  <span class="detail-value error-text">{{ printJobStore.multiMaterialJob.drums[drumId].errorMessage }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Coordination Status -->
        <div class="coordination-status-section" v-if="printJobStore.isJobActive">
          <h4 class="section-title">Coordination Status</h4>
          <div class="coordination-grid">
            <div class="coordination-item">
              <span class="coordination-label">All Drums Ready:</span>
              <span :class="['coordination-value', printJobStore.allDrumsReady ? 'ready' : 'waiting']">
                {{ printJobStore.allDrumsReady ? 'Yes' : 'Waiting...' }}
              </span>
            </div>
            <div class="coordination-item">
              <span class="coordination-label">Job Status:</span>
              <span :class="['coordination-value', getJobStatusClass()]">
                {{ getJobStatusText() }}
              </span>
            </div>
            <div class="coordination-item" v-if="printJobStore.multiMaterialJob.errorMessage">
              <span class="coordination-label">System Message:</span>
              <span class="coordination-value error-text">
                {{ printJobStore.multiMaterialJob.errorMessage }}
              </span>
            </div>
          </div>
        </div>

        <!-- No Active Job Message -->
        <div v-if="!printJobStore.hasActiveJob" class="no-job-message">
          
          <div class="no-job-text">
            <h4>No Active Job</h4>
            <p>Upload CLI files and start a multi-material job to see progress here.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { usePrintJobStore } from '../stores/printJobStore'

export default {
  name: 'JobProgressDisplay',
  setup() {
    const printJobStore = usePrintJobStore()
    
    // Auto-refresh interval
    let refreshInterval = null

    // Methods
    const formatTime = (timestamp) => {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleTimeString()
    }

    const getElapsedTime = () => {
      if (!printJobStore.multiMaterialJob.startTime) return 'N/A'
      const elapsed = Date.now() - printJobStore.multiMaterialJob.startTime
      return formatDuration(elapsed)
    }

    const formatDuration = (milliseconds) => {
      if (!milliseconds) return 'N/A'
      
      const seconds = Math.floor(milliseconds / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      
      if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`
      } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`
      } else {
        return `${seconds}s`
      }
    }

    const getDrumStatusClass = (drumId) => {
      const drum = printJobStore.multiMaterialJob.drums[drumId]
      if (drum.errorMessage) return 'status-error'
      if (drum.ready) return 'status-ready'
      if (drum.uploaded) return 'status-uploaded'
      return 'status-idle'
    }

    const getDrumStatusText = (drumId) => {
      const drum = printJobStore.multiMaterialJob.drums[drumId]
      if (drum.errorMessage) return 'Error'
      if (drum.ready) return 'Ready'
      if (drum.uploaded) return 'Uploaded'
      return 'Idle'
    }

    const getDrumCardClass = (drumId) => {
      const drum = printJobStore.multiMaterialJob.drums[drumId]
      if (drum.errorMessage) return 'drum-error'
      if (drum.ready) return 'drum-ready'
      if (drum.uploaded) return 'drum-uploaded'
      return 'drum-idle'
    }

    const getJobStatusClass = () => {
      const status = printJobStore.multiMaterialJob.status
      if (status === 'printing') return 'status-active'
      if (status === 'error') return 'status-error'
      if (status === 'uploading') return 'status-uploading'
      if (status === 'waiting') return 'status-waiting'
      return 'status-ready'
    }

    const getJobStatusText = () => {
      const status = printJobStore.multiMaterialJob.status
      switch (status) {
        case 'printing': return 'Printing'
        case 'uploading': return 'Uploading Layers'
        case 'waiting': return 'Waiting for Print Start'
        case 'completed': return 'Completed'
        case 'error': return 'Error'
        default: return 'Ready'
      }
    }

    // Lifecycle
    onMounted(() => {
      // Start auto-refresh for active jobs
      refreshInterval = setInterval(() => {
        if (printJobStore.hasActiveJob) {
          printJobStore.fetchJobStatus().catch(console.error)
        }
      }, 2000) // Refresh every 2 seconds for real-time updates
    })

    onUnmounted(() => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    })

    return {
      printJobStore,
      formatTime,
      getElapsedTime,
      formatDuration,
      getDrumStatusClass,
      getDrumStatusText,
      getDrumCardClass,
      getJobStatusClass,
      getJobStatusText
    }
  }
}
</script>

<style scoped>
.job-progress-display {
  margin-bottom: 2rem;
}

.progress-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background: #f8f9fa;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  color: #2c3e50;
}

.job-id {
  font-size: 0.875rem;
  color: #6c757d;
  font-family: monospace;
}

.card-content {
  padding: 1.5rem;
}

.section-title {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.overall-progress-section {
  margin-bottom: 2rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-stats {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.current-layer {
  color: #495057;
  font-weight: 600;
}

.progress-percentage {
  color: #007bff;
  font-weight: 700;
  font-size: 1.25rem;
}

.progress-bar-container {
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.time-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.time-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.time-value {
  font-weight: 600;
  color: #495057;
}

.drum-status-section {
  margin-bottom: 2rem;
}

.drum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.drum-status-card {
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s;
}

.drum-idle { border-color: #6c757d; }
.drum-uploaded { border-color: #17a2b8; }
.drum-ready { border-color: #28a745; }
.drum-error { border-color: #dc3545; }

.drum-header {
  margin-bottom: 1rem;
}

.drum-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drum-title {
  font-weight: 600;
  color: #495057;
}

.drum-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-idle { background-color: #6c757d; }
.status-uploaded { background-color: #17a2b8; }
.status-ready { background-color: #28a745; }
.status-error { background-color: #dc3545; }
.status-active { background-color: #007bff; }
.status-uploading { background-color: #ffc107; }
.status-waiting { background-color: #fd7e14; }

.status-text {
  font-size: 0.875rem;
  color: #6c757d;
}

.drum-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.detail-value {
  font-weight: 600;
  color: #495057;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

.status-icon.ready {
  color: #28a745;
}

.status-icon.not-ready {
  color: #6c757d;
}

.error-text {
  color: #dc3545 !important;
}

.coordination-status-section {
  margin-bottom: 2rem;
}

.coordination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.coordination-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.coordination-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.coordination-value {
  font-weight: 600;
}

.coordination-value.ready {
  color: #28a745;
}

.coordination-value.waiting {
  color: #ffc107;
}

.coordination-value.status-active {
  color: #007bff;
}

.coordination-value.status-error {
  color: #dc3545;
}

.coordination-value.status-uploading {
  color: #ffc107;
}

.coordination-value.status-waiting {
  color: #fd7e14;
}

.no-job-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.no-job-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-job-text h4 {
  margin: 0 0 0.5rem 0;
  color: #495057;
}

.no-job-text p {
  margin: 0;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .progress-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .drum-grid {
    grid-template-columns: 1fr;
  }
  
  .coordination-grid {
    grid-template-columns: 1fr;
  }
  
  .coordination-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
