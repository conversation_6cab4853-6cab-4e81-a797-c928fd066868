import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import CriticalErrorModal from '../CriticalErrorModal.vue'
import { usePrintJobStore } from '../../stores/printJobStore'

describe('CriticalErrorModal', () => {
  let wrapper
  let mockPrintJobStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    wrapper = mount(CriticalErrorModal, {
      attachTo: document.body
    })

    mockPrintJobStore = usePrintJobStore()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Modal Visibility', () => {
    it('does not render when no critical error', () => {
      expect(wrapper.find('.critical-error-modal-overlay').exists()).toBe(false)
    })

    it('renders when critical error is present', async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Backend error occurred')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.critical-error-modal-overlay').exists()).toBe(true)
      expect(wrapper.find('.critical-error-modal').exists()).toBe(true)
    })

    it('shows modal when either backend or PLC error is set', async () => {
      // Test backend error
      mockPrintJobStore.setErrorFlags(true, false, 'Backend error')
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.critical-error-modal-overlay').exists()).toBe(true)

      // Clear and test PLC error
      mockPrintJobStore.clearErrorFlags()
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.critical-error-modal-overlay').exists()).toBe(false)

      mockPrintJobStore.setErrorFlags(false, true, 'PLC error')
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.critical-error-modal-overlay').exists()).toBe(true)
    })
  })

  describe('Modal Content', () => {
    beforeEach(async () => {
      mockPrintJobStore.setErrorFlags(true, true, 'Multiple system errors detected')
      await wrapper.vm.$nextTick()
    })

    it('displays correct modal title', () => {
      expect(wrapper.find('#error-modal-title').text()).toBe('Critical System Error')
    })

    it('shows backend error flag when present', () => {
      const backendError = wrapper.find('.backend-error')
      expect(backendError.exists()).toBe(true)
      expect(backendError.find('.flag-title').text()).toBe('Backend Error')
      expect(backendError.find('.flag-description').text()).toContain('recoater backend system')
    })

    it('shows PLC error flag when present', () => {
      const plcError = wrapper.find('.plc-error')
      expect(plcError.exists()).toBe(true)
      expect(plcError.find('.flag-title').text()).toBe('PLC Error')
      expect(plcError.find('.flag-description').text()).toContain('PLC system')
    })

    it('displays error message when provided', () => {
      const errorMessageBox = wrapper.find('.error-message-box')
      expect(errorMessageBox.exists()).toBe(true)
      expect(errorMessageBox.text()).toBe('Multiple system errors detected')
    })

    it('shows impact section with correct items', () => {
      const impactItems = wrapper.findAll('.impact-item')
      expect(impactItems).toHaveLength(3)
      
      expect(impactItems[0].text()).toContain('All print operations have been paused')
      expect(impactItems[1].text()).toContain('New jobs cannot be started')
      expect(impactItems[2].text()).toContain('Operator intervention is required')
    })

    it('shows action steps section', () => {
      const actionSteps = wrapper.findAll('.action-step')
      expect(actionSteps).toHaveLength(3)
      
      expect(actionSteps[0].find('.step-title').text()).toBe('Investigate the Error')
      expect(actionSteps[1].find('.step-title').text()).toBe('Resolve the Issue')
      expect(actionSteps[2].find('.step-title').text()).toBe('Clear Error Flags')
    })
  })

  describe('Modal Actions', () => {
    beforeEach(async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()
    })

    it('renders close button', () => {
      const closeButton = wrapper.find('[data-testid="close-error-modal-btn"]')
      expect(closeButton.exists()).toBe(true)
      expect(closeButton.attributes('aria-label')).toBe('Close error modal')
    })

    it('renders clear error flags button', () => {
      const clearButton = wrapper.find('[data-testid="clear-error-flags-btn"]')
      expect(clearButton.exists()).toBe(true)
      expect(clearButton.text()).toBe('Clear Error Flags')
    })

    it('renders acknowledge button', () => {
      const acknowledgeButton = wrapper.find('[data-testid="acknowledge-error-btn"]')
      expect(acknowledgeButton.exists()).toBe(true)
      expect(acknowledgeButton.text()).toBe('Acknowledge')
    })

    it('closes modal when close button is clicked', async () => {
      const mockCloseCriticalModal = vi.spyOn(mockPrintJobStore, 'closeCriticalModal')
      
      const closeButton = wrapper.find('[data-testid="close-error-modal-btn"]')
      await closeButton.trigger('click')

      expect(mockCloseCriticalModal).toHaveBeenCalled()
    })

    it('closes modal when acknowledge button is clicked', async () => {
      const mockCloseCriticalModal = vi.spyOn(mockPrintJobStore, 'closeCriticalModal')
      
      const acknowledgeButton = wrapper.find('[data-testid="acknowledge-error-btn"]')
      await acknowledgeButton.trigger('click')

      expect(mockCloseCriticalModal).toHaveBeenCalled()
    })

    it('calls clearErrorFlagsAPI when clear button is clicked', async () => {
      const mockClearErrorFlags = vi.spyOn(mockPrintJobStore, 'clearErrorFlagsAPI')
        .mockResolvedValue({ success: true })
      
      const clearButton = wrapper.find('[data-testid="clear-error-flags-btn"]')
      await clearButton.trigger('click')

      expect(mockClearErrorFlags).toHaveBeenCalled()
    })

    it('shows loading state on clear button when clearing errors', async () => {
      mockPrintJobStore.isClearingErrors = true
      await wrapper.vm.$nextTick()

      const clearButton = wrapper.find('[data-testid="clear-error-flags-btn"]')
      expect(clearButton.text()).toBe('Clearing...')
      expect(clearButton.element.disabled).toBe(true)
    })
  })

  describe('Modal Behavior', () => {
    beforeEach(async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()
    })

    it('prevents closing modal by clicking overlay', async () => {
      const mockCloseCriticalModal = vi.spyOn(mockPrintJobStore, 'closeCriticalModal')
      
      const overlay = wrapper.find('.critical-error-modal-overlay')
      await overlay.trigger('click')

      // Should not close modal when clicking overlay for critical errors
      expect(mockCloseCriticalModal).not.toHaveBeenCalled()
    })

    it('has correct ARIA attributes for accessibility', () => {
      const modal = wrapper.find('.critical-error-modal')
      expect(modal.attributes('role')).toBe('dialog')
      expect(modal.attributes('aria-modal')).toBe('true')
      expect(modal.attributes('aria-labelledby')).toBe('error-modal-title')
    })

    it('displays timestamp in footer', () => {
      const timestamp = wrapper.find('.timestamp')
      expect(timestamp.exists()).toBe(true)
      expect(timestamp.text()).toContain('Error occurred at:')
    })
  })

  describe('Error Type Display', () => {
    it('shows only backend error when only backend error is set', async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Backend only error')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.backend-error').exists()).toBe(true)
      expect(wrapper.find('.plc-error').exists()).toBe(false)
    })

    it('shows only PLC error when only PLC error is set', async () => {
      mockPrintJobStore.setErrorFlags(false, true, 'PLC only error')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.backend-error').exists()).toBe(false)
      expect(wrapper.find('.plc-error').exists()).toBe(true)
    })

    it('shows both errors when both are set', async () => {
      mockPrintJobStore.setErrorFlags(true, true, 'Both errors')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.backend-error').exists()).toBe(true)
      expect(wrapper.find('.plc-error').exists()).toBe(true)
    })

    it('hides error message section when no message provided', async () => {
      mockPrintJobStore.setErrorFlags(true, false, '')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.error-message-section').exists()).toBe(false)
    })
  })

  describe('Responsive Design', () => {
    it('applies mobile styles correctly', async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()

      // Check that modal has responsive classes
      const modal = wrapper.find('.critical-error-modal')
      expect(modal.exists()).toBe(true)
      
      // The CSS media queries would be tested in e2e tests
      // Here we just verify the structure is correct
      expect(wrapper.find('.modal-footer').exists()).toBe(true)
      expect(wrapper.find('.footer-actions').exists()).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('handles clearErrorFlagsAPI failure gracefully', async () => {
      const mockClearErrorFlags = vi.spyOn(mockPrintJobStore, 'clearErrorFlagsAPI')
        .mockRejectedValue(new Error('API call failed'))
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()

      const clearButton = wrapper.find('[data-testid="clear-error-flags-btn"]')
      await clearButton.trigger('click')

      expect(mockClearErrorFlags).toHaveBeenCalled()
      expect(consoleSpy).toHaveBeenCalledWith('Failed to clear error flags:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('Animation and Styling', () => {
    it('applies correct CSS classes for modal animation', async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()

      const modal = wrapper.find('.critical-error-modal')
      expect(modal.classes()).toContain('critical-error-modal')
      
      const overlay = wrapper.find('.critical-error-modal-overlay')
      expect(overlay.classes()).toContain('critical-error-modal-overlay')
    })

    it('has correct z-index for modal overlay', async () => {
      mockPrintJobStore.setErrorFlags(true, false, 'Test error')
      await wrapper.vm.$nextTick()

      const overlay = wrapper.find('.critical-error-modal-overlay')
      expect(overlay.exists()).toBe(true)
      // CSS z-index would be verified in the actual CSS, here we just check structure
    })
  })
})
