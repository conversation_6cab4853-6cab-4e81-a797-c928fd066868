"""
Status Polling Service
=====================

Handles background polling of recoater status and broadcasting updates via WebSocket.
Manages the polling lifecycle and error handling.
"""

import os
import asyncio
import logging
from typing import Optional

from services.recoater_client import RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client_instance
from app.services.websocket_manager import WebSocketConnectionManager
from app.services.data_gatherer import RecoaterDataGatherer

logger = logging.getLogger(__name__)


class StatusPollingService:
    """Manages background status polling and WebSocket broadcasting."""

    def __init__(self, websocket_manager: WebSocketConnectionManager):
        """
        Initialize the status polling service.
        
        Args:
            websocket_manager: The WebSocket connection manager instance
        """
        self.websocket_manager = websocket_manager
        self.data_gatherer = RecoaterDataGatherer()
        self.poll_interval = float(os.getenv("WEBSOCKET_POLL_INTERVAL", "1.0"))
        self.polling_task: Optional[asyncio.Task] = None
        self._running = False

    async def start(self) -> None:
        """Start the status polling task."""
        if self._running:
            logger.warning("Status polling is already running")
            return
            
        self._running = True
        self.polling_task = asyncio.create_task(self._polling_loop())
        logger.info("Status polling task started")

    async def stop(self) -> None:
        """Stop the status polling task."""
        if not self._running:
            logger.warning("Status polling is not running")
            return
            
        self._running = False
        if self.polling_task:
            self.polling_task.cancel()
            try:
                await self.polling_task
            except asyncio.CancelledError:
                pass
            self.polling_task = None
        logger.info("Status polling task stopped")

    async def _polling_loop(self) -> None:
        """Main polling loop that runs in the background."""
        while self._running:
            try:
                await self._poll_and_broadcast()
            except (RecoaterConnectionError, RecoaterAPIError) as e:
                logger.error(f"Status polling connection error: {e}")
                await self._broadcast_connection_error(e)
            except Exception as e:
                logger.error(f"Unexpected error in status polling: {e}", exc_info=True)

            await asyncio.sleep(self.poll_interval)

    async def _poll_and_broadcast(self) -> None:
        """Poll recoater status and broadcast updates."""
        recoater_client = get_recoater_client_instance()

        if not recoater_client or not self.websocket_manager.has_connections:
            return

        # Get required data types from all active connections
        required_data_types = self.websocket_manager.get_required_data_types()
        logger.debug(f"Required data types: {list(required_data_types)}")

        # Gather all required data
        gathered_data = await self.data_gatherer.gather_all_data(
            recoater_client, required_data_types
        )

        # Construct and broadcast message
        message = self.data_gatherer.construct_status_message(gathered_data)
        await self.websocket_manager.broadcast(message)

    async def _broadcast_connection_error(self, error: Exception) -> None:
        """
        Broadcast a connection error to all connected clients.
        
        Args:
            error: The error that occurred
        """
        error_message = {
            "type": "connection_error",
            "error": str(error),
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.websocket_manager.broadcast(error_message)

    @property
    def is_running(self) -> bool:
        """Check if the polling service is currently running."""
        return self._running

    def update_poll_interval(self, interval: float) -> None:
        """
        Update the polling interval.
        
        Args:
            interval: New polling interval in seconds
        """
        self.poll_interval = interval
        logger.info(f"Updated polling interval to {interval} seconds")
